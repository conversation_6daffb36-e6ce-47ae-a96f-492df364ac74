"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/PackagesCarousel.tsx":
/*!******************************************************!*\
  !*** ./src/components/sections/PackagesCarousel.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PackagesCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Filter,MapPin,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Filter,MapPin,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Filter,MapPin,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Filter,MapPin,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Filter,MapPin,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Filter,MapPin,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Filter,MapPin,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _data_content__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/content */ \"(app-pages-browser)/./src/data/content.ts\");\n/* harmony import */ var _components_ui_PlaceholderImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/PlaceholderImage */ \"(app-pages-browser)/./src/components/ui/PlaceholderImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction PackagesCarousel() {\n    _s();\n    const [activeFilters, setActiveFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: 'All Packages',\n        region: 'All Regions',\n        duration: 'Any Duration',\n        budget: 'Any Budget'\n    });\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Filter packages based on active filters\n    const filteredPackages = _data_content__WEBPACK_IMPORTED_MODULE_2__.PACKAGES_DATA.filter((pkg)=>{\n        const categoryMatch = activeFilters.category === 'All Packages' || pkg.category === activeFilters.category;\n        const regionMatch = activeFilters.region === 'All Regions' || pkg.region === activeFilters.region;\n        // Duration filter logic\n        let durationMatch = true;\n        if (activeFilters.duration !== 'Any Duration') {\n            const pkgDays = parseInt(pkg.duration.split(' ')[0]);\n            switch(activeFilters.duration){\n                case '1-2 Days':\n                    durationMatch = pkgDays <= 2;\n                    break;\n                case '3-4 Days':\n                    durationMatch = pkgDays >= 3 && pkgDays <= 4;\n                    break;\n                case '5-6 Days':\n                    durationMatch = pkgDays >= 5 && pkgDays <= 6;\n                    break;\n                case '7+ Days':\n                    durationMatch = pkgDays >= 7;\n                    break;\n            }\n        }\n        // Budget filter logic\n        let budgetMatch = true;\n        if (activeFilters.budget !== 'Any Budget') {\n            const priceStr = pkg.priceRange.split(' - ')[0].replace('₹', '').replace(',', '');\n            const price = parseInt(priceStr);\n            switch(activeFilters.budget){\n                case 'Under ₹15,000':\n                    budgetMatch = price < 15000;\n                    break;\n                case '₹15,000 - ₹25,000':\n                    budgetMatch = price >= 15000 && price <= 25000;\n                    break;\n                case '₹25,000 - ₹40,000':\n                    budgetMatch = price >= 25000 && price <= 40000;\n                    break;\n                case 'Above ₹40,000':\n                    budgetMatch = price > 40000;\n                    break;\n            }\n        }\n        return categoryMatch && regionMatch && durationMatch && budgetMatch;\n    });\n    const handleFilterChange = (filterType, value)=>{\n        setActiveFilters((prev)=>({\n                ...prev,\n                [filterType]: value\n            }));\n        setCurrentIndex(0); // Reset carousel position when filters change\n    };\n    const clearAllFilters = ()=>{\n        setActiveFilters({\n            category: 'All Packages',\n            region: 'All Regions',\n            duration: 'Any Duration',\n            budget: 'Any Budget'\n        });\n        setCurrentIndex(0);\n    };\n    const nextSlide = ()=>{\n        setCurrentIndex((prev)=>prev >= filteredPackages.length - 1 ? 0 : prev + 1);\n    };\n    const prevSlide = ()=>{\n        setCurrentIndex((prev)=>prev <= 0 ? filteredPackages.length - 1 : prev - 1);\n    };\n    // Auto-scroll functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PackagesCarousel.useEffect\": ()=>{\n            const interval = setInterval({\n                \"PackagesCarousel.useEffect.interval\": ()=>{\n                    if (filteredPackages.length > 1) {\n                        setCurrentIndex({\n                            \"PackagesCarousel.useEffect.interval\": (prev)=>prev >= filteredPackages.length - 1 ? 0 : prev + 1\n                        }[\"PackagesCarousel.useEffect.interval\"]);\n                    }\n                }\n            }[\"PackagesCarousel.useEffect.interval\"], 5000); // Auto-scroll every 5 seconds\n            return ({\n                \"PackagesCarousel.useEffect\": ()=>clearInterval(interval)\n            })[\"PackagesCarousel.useEffect\"];\n        }\n    }[\"PackagesCarousel.useEffect\"], [\n        filteredPackages.length\n    ]);\n    const handleCustomizeClick = (packageId)=>{\n        // Scroll to booking form or open customization modal\n        const bookingForm = document.getElementById('booking-form');\n        if (bookingForm) {\n            bookingForm.scrollIntoView({\n                behavior: 'smooth'\n            });\n            // You can also pass the package ID to pre-fill the form\n            console.log('Customizing package:', packageId);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"packages\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-heading font-bold text-neutral-900 mb-4\",\n                            children: \"Discover Your Perfect Journey\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-neutral-600 max-w-2xl mx-auto mb-8\",\n                            children: \"Handcrafted travel experiences designed for Indian families. Choose from our curated packages or customize your own adventure.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setShowFilters(!showFilters),\n                            className: \"inline-flex items-center gap-2 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                showFilters ? 'Hide Filters' : 'Show Filters'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"bg-neutral-50 rounded-xl p-6 mb-8 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-neutral-700 mb-2\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: activeFilters.category,\n                                                onChange: (e)=>handleFilterChange('category', e.target.value),\n                                                className: \"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                                children: _data_content__WEBPACK_IMPORTED_MODULE_2__.PACKAGE_CATEGORIES.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category,\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-neutral-700 mb-2\",\n                                                children: \"Region\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: activeFilters.region,\n                                                onChange: (e)=>handleFilterChange('region', e.target.value),\n                                                className: \"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                                children: _data_content__WEBPACK_IMPORTED_MODULE_2__.PACKAGE_FILTERS.regions.map((region)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: region,\n                                                        children: region\n                                                    }, region, false, {\n                                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-neutral-700 mb-2\",\n                                                children: \"Duration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: activeFilters.duration,\n                                                onChange: (e)=>handleFilterChange('duration', e.target.value),\n                                                className: \"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                                children: _data_content__WEBPACK_IMPORTED_MODULE_2__.PACKAGE_FILTERS.duration.map((duration)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: duration,\n                                                        children: duration\n                                                    }, duration, false, {\n                                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-neutral-700 mb-2\",\n                                                children: \"Budget\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: activeFilters.budget,\n                                                onChange: (e)=>handleFilterChange('budget', e.target.value),\n                                                className: \"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                                children: _data_content__WEBPACK_IMPORTED_MODULE_2__.PACKAGE_FILTERS.budget.map((budget)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: budget,\n                                                        children: budget\n                                                    }, budget, false, {\n                                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: clearAllFilters,\n                                className: \"inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Clear All Filters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-600\",\n                        children: [\n                            \"Showing \",\n                            filteredPackages.length,\n                            \" of \",\n                            _data_content__WEBPACK_IMPORTED_MODULE_2__.PACKAGES_DATA.length,\n                            \" packages\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: filteredPackages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            filteredPackages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: prevSlide,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white shadow-lg rounded-full p-3 transition-all duration-300\",\n                                        \"aria-label\": \"Previous package\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-neutral-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: nextSlide,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white shadow-lg rounded-full p-3 transition-all duration-300\",\n                                        \"aria-label\": \"Next package\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-neutral-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: carouselRef,\n                                className: \"overflow-hidden rounded-xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"flex transition-transform duration-500 ease-in-out\",\n                                    style: {\n                                        transform: \"translateX(-\".concat(currentIndex * 100, \"%)\")\n                                    },\n                                    children: filteredPackages.map((pkg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PackageCard, {\n                                                package: pkg,\n                                                onCustomize: ()=>handleCustomizeClick(pkg.id)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, pkg.id, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, this),\n                            filteredPackages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6 gap-2\",\n                                children: filteredPackages.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setCurrentIndex(index),\n                                        className: \"w-3 h-3 rounded-full transition-all duration-300 \".concat(index === currentIndex ? 'bg-primary-600 scale-110' : 'bg-neutral-300 hover:bg-neutral-400'),\n                                        \"aria-label\": \"Go to slide \".concat(index + 1)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-600 text-lg\",\n                                children: \"No packages match your current filters. Try adjusting your criteria.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: clearAllFilters,\n                                className: \"mt-4 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-300\",\n                                children: \"Clear All Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(PackagesCarousel, \"ltbO6s0WPhBfLFK+uC3cU2rE850=\");\n_c = PackagesCarousel;\nfunction PackageCard(param) {\n    let { package: pkg, onCustomize } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-64 lg:h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PlaceholderImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: pkg.title,\n                            width: 400,\n                            height: 300,\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this),\n                        pkg.isPopular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-4 left-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                            children: \"Popular Choice\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4 text-yellow-500 fill-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium\",\n                                    children: pkg.rating\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-neutral-600\",\n                                    children: [\n                                        \"(\",\n                                        pkg.reviewCount,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm font-medium mb-3\",\n                                    children: pkg.category\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-heading font-bold text-neutral-900 mb-2\",\n                                    children: pkg.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-neutral-600 leading-relaxed mb-4\",\n                                    children: pkg.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 mb-6 text-sm text-neutral-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: pkg.duration\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Filter_MapPin_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: pkg.region\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-neutral-900 mb-3\",\n                                    children: \"Package Highlights:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: pkg.highlights.map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-neutral-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1.5 h-1.5 bg-primary-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: highlight\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 mb-6\",\n                            children: pkg.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-neutral-100 text-neutral-700 px-2 py-1 rounded text-xs\",\n                                    children: tag\n                                }, index, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-neutral-600\",\n                                            children: \"Starting from\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-primary-600\",\n                                            children: pkg.priceRange.split(' - ')[0]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-neutral-500\",\n                                            children: \"per person\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onCustomize,\n                                    className: \"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300\",\n                                    children: \"Customize Trip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\PackagesCarousel.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, this);\n}\n_c1 = PackageCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"PackagesCarousel\");\n$RefreshReg$(_c1, \"PackageCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/PackagesCarousel.tsx\n"));

/***/ })

});