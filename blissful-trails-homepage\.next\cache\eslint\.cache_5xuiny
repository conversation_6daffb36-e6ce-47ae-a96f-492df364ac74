[{"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx": "1", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx": "2", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx": "3", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HowItWorksSection.tsx": "4", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\PackagesCarousel.tsx": "5", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx": "6", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\PlaceholderImage.tsx": "7", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\StyleTest.tsx": "8", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\data\\content.ts": "9", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\constants.ts": "10", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\utils.ts": "11", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\types\\index.ts": "12"}, {"size": 1315, "mtime": 1752771289723, "results": "13", "hashOfConfig": "14"}, {"size": 818, "mtime": 1752771442549, "results": "15", "hashOfConfig": "14"}, {"size": 4171, "mtime": 1752769929786, "results": "16", "hashOfConfig": "14"}, {"size": 14368, "mtime": 1752771379532, "results": "17", "hashOfConfig": "14"}, {"size": 16543, "mtime": 1752771467186, "results": "18", "hashOfConfig": "14"}, {"size": 2259, "mtime": 1752769847269, "results": "19", "hashOfConfig": "14"}, {"size": 1720, "mtime": 1752770355077, "results": "20", "hashOfConfig": "14"}, {"size": 2194, "mtime": 1752771161161, "results": "21", "hashOfConfig": "14"}, {"size": 8453, "mtime": 1752771322844, "results": "22", "hashOfConfig": "14"}, {"size": 550, "mtime": 1752769294386, "results": "23", "hashOfConfig": "14"}, {"size": 166, "mtime": 1752769335996, "results": "24", "hashOfConfig": "14"}, {"size": 552, "mtime": 1752769493152, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kfznf5", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HowItWorksSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\PackagesCarousel.tsx", ["62"], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\PlaceholderImage.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\StyleTest.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\data\\content.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\constants.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\utils.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\types\\index.ts", [], [], {"ruleId": "63", "severity": 1, "message": "64", "line": 291, "column": 47, "nodeType": null, "messageId": "65", "endLine": 291, "endColumn": 52}, "@typescript-eslint/no-unused-vars", "'index' is defined but never used.", "unusedVar"]