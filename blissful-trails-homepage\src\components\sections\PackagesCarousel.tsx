'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star, Clock, MapPin, Filter, X } from 'lucide-react';
import { PACKAGES_DATA, PACKAGE_CATEGORIES, PACKAGE_FILTERS } from '@/data/content';
import PlaceholderImage from '@/components/ui/PlaceholderImage';

interface FilterState {
  category: string;
  region: string;
  duration: string;
  budget: string;
}

export default function PackagesCarousel() {
  const [activeFilters, setActiveFilters] = useState<FilterState>({
    category: 'All Packages',
    region: 'All Regions',
    duration: 'Any Duration',
    budget: 'Any Budget'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const carouselRef = useRef<HTMLDivElement>(null);

  // Filter packages based on active filters
  const filteredPackages = PACKAGES_DATA.filter(pkg => {
    const categoryMatch = activeFilters.category === 'All Packages' || pkg.category === activeFilters.category;
    const regionMatch = activeFilters.region === 'All Regions' || pkg.region === activeFilters.region;
    
    // Duration filter logic
    let durationMatch = true;
    if (activeFilters.duration !== 'Any Duration') {
      const pkgDays = parseInt(pkg.duration.split(' ')[0]);
      switch (activeFilters.duration) {
        case '1-2 Days':
          durationMatch = pkgDays <= 2;
          break;
        case '3-4 Days':
          durationMatch = pkgDays >= 3 && pkgDays <= 4;
          break;
        case '5-6 Days':
          durationMatch = pkgDays >= 5 && pkgDays <= 6;
          break;
        case '7+ Days':
          durationMatch = pkgDays >= 7;
          break;
      }
    }

    // Budget filter logic
    let budgetMatch = true;
    if (activeFilters.budget !== 'Any Budget') {
      const priceStr = pkg.priceRange.split(' - ')[0].replace('₹', '').replace(',', '');
      const price = parseInt(priceStr);
      switch (activeFilters.budget) {
        case 'Under ₹15,000':
          budgetMatch = price < 15000;
          break;
        case '₹15,000 - ₹25,000':
          budgetMatch = price >= 15000 && price <= 25000;
          break;
        case '₹25,000 - ₹40,000':
          budgetMatch = price >= 25000 && price <= 40000;
          break;
        case 'Above ₹40,000':
          budgetMatch = price > 40000;
          break;
      }
    }

    return categoryMatch && regionMatch && durationMatch && budgetMatch;
  });

  const handleFilterChange = (filterType: keyof FilterState, value: string) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
    setCurrentIndex(0); // Reset carousel position when filters change
  };

  const clearAllFilters = () => {
    setActiveFilters({
      category: 'All Packages',
      region: 'All Regions',
      duration: 'Any Duration',
      budget: 'Any Budget'
    });
    setCurrentIndex(0);
  };

  const nextSlide = () => {
    setCurrentIndex((prev) => 
      prev >= filteredPackages.length - 1 ? 0 : prev + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => 
      prev <= 0 ? filteredPackages.length - 1 : prev - 1
    );
  };

  // Auto-scroll functionality
  useEffect(() => {
    const interval = setInterval(() => {
      if (filteredPackages.length > 1) {
        setCurrentIndex((prev) =>
          prev >= filteredPackages.length - 1 ? 0 : prev + 1
        );
      }
    }, 5000); // Auto-scroll every 5 seconds

    return () => clearInterval(interval);
  }, [filteredPackages.length]);

  const handleCustomizeClick = (packageId: string) => {
    // Scroll to booking form or open customization modal
    const bookingForm = document.getElementById('booking-form');
    if (bookingForm) {
      bookingForm.scrollIntoView({ behavior: 'smooth' });
      // You can also pass the package ID to pre-fill the form
      console.log('Customizing package:', packageId);
    }
  };

  return (
    <section id="packages" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 mb-4">
            Discover Your Perfect Journey
          </h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto mb-8">
            Handcrafted travel experiences designed for Indian families. Choose from our curated packages or customize your own adventure.
          </p>

          {/* Filter Toggle Button */}
          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center gap-2 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-300"
          >
            <Filter className="w-5 h-5" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </motion.div>

        {/* Filter Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-neutral-50 rounded-xl p-6 mb-8 overflow-hidden"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Category
                  </label>
                  <select
                    value={activeFilters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    className="w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    {PACKAGE_CATEGORIES.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                {/* Region Filter */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Region
                  </label>
                  <select
                    value={activeFilters.region}
                    onChange={(e) => handleFilterChange('region', e.target.value)}
                    className="w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    {PACKAGE_FILTERS.regions.map(region => (
                      <option key={region} value={region}>{region}</option>
                    ))}
                  </select>
                </div>

                {/* Duration Filter */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Duration
                  </label>
                  <select
                    value={activeFilters.duration}
                    onChange={(e) => handleFilterChange('duration', e.target.value)}
                    className="w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    {PACKAGE_FILTERS.duration.map(duration => (
                      <option key={duration} value={duration}>{duration}</option>
                    ))}
                  </select>
                </div>

                {/* Budget Filter */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Budget
                  </label>
                  <select
                    value={activeFilters.budget}
                    onChange={(e) => handleFilterChange('budget', e.target.value)}
                    className="w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    {PACKAGE_FILTERS.budget.map(budget => (
                      <option key={budget} value={budget}>{budget}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Clear Filters Button */}
              <button
                type="button"
                onClick={clearAllFilters}
                className="inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium"
              >
                <X className="w-4 h-4" />
                Clear All Filters
              </button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Results Count */}
        <div className="text-center mb-8">
          <p className="text-neutral-600">
            Showing {filteredPackages.length} of {PACKAGES_DATA.length} packages
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {filteredPackages.length > 0 ? (
            <>
              {/* Navigation Buttons */}
              {filteredPackages.length > 1 && (
                <>
                  <button
                    type="button"
                    onClick={prevSlide}
                    className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white shadow-lg rounded-full p-3 transition-all duration-300"
                    aria-label="Previous package"
                  >
                    <ChevronLeft className="w-6 h-6 text-neutral-700" />
                  </button>
                  <button
                    type="button"
                    onClick={nextSlide}
                    className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white shadow-lg rounded-full p-3 transition-all duration-300"
                    aria-label="Next package"
                  >
                    <ChevronRight className="w-6 h-6 text-neutral-700" />
                  </button>
                </>
              )}

              {/* Carousel Content */}
              <div
                ref={carouselRef}
                className="overflow-hidden rounded-xl"
              >
                <motion.div
                  className="flex transition-transform duration-500 ease-in-out"
                  style={{
                    transform: `translateX(-${currentIndex * 100}%)`
                  }}
                >
                  {filteredPackages.map((pkg) => (
                    <div key={pkg.id} className="w-full flex-shrink-0">
                      <PackageCard
                        package={pkg}
                        onCustomize={() => handleCustomizeClick(pkg.id)}
                      />
                    </div>
                  ))}
                </motion.div>
              </div>

              {/* Carousel Indicators */}
              {filteredPackages.length > 1 && (
                <div className="flex justify-center mt-6 gap-2">
                  {filteredPackages.map((_, index) => (
                    <button
                      type="button"
                      key={index}
                      onClick={() => setCurrentIndex(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentIndex
                          ? 'bg-primary-600 scale-110'
                          : 'bg-neutral-300 hover:bg-neutral-400'
                      }`}
                      aria-label={`Go to slide ${index + 1}`}
                    />
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-neutral-600 text-lg">
                No packages match your current filters. Try adjusting your criteria.
              </p>
              <button
                type="button"
                onClick={clearAllFilters}
                className="mt-4 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-300"
              >
                Clear All Filters
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}

// Package Card Component
interface PackageCardProps {
  package: (typeof PACKAGES_DATA)[number];
  onCustomize: () => void;
}

function PackageCard({ package: pkg, onCustomize }: PackageCardProps) {
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
        {/* Image Section */}
        <div className="relative h-64 lg:h-full">
          <PlaceholderImage
            title={pkg.title}
            width={400}
            height={300}
            className="w-full h-full object-cover"
          />
          {pkg.isPopular && (
            <div className="absolute top-4 left-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-medium">
              Popular Choice
            </div>
          )}
          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span className="text-sm font-medium">{pkg.rating}</span>
            <span className="text-xs text-neutral-600">({pkg.reviewCount})</span>
          </div>
        </div>

        {/* Content Section */}
        <div className="p-8">
          <div className="mb-4">
            <span className="inline-block bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm font-medium mb-3">
              {pkg.category}
            </span>
            <h3 className="text-2xl font-heading font-bold text-neutral-900 mb-2">
              {pkg.title}
            </h3>
            <p className="text-neutral-600 leading-relaxed mb-4">
              {pkg.description}
            </p>
          </div>

          {/* Package Details */}
          <div className="flex flex-wrap gap-4 mb-6 text-sm text-neutral-600">
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>{pkg.duration}</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              <span>{pkg.region}</span>
            </div>
          </div>

          {/* Highlights */}
          <div className="mb-6">
            <h4 className="font-semibold text-neutral-900 mb-3">Package Highlights:</h4>
            <div className="grid grid-cols-2 gap-2">
              {pkg.highlights.map((highlight, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-neutral-600">
                  <div className="w-1.5 h-1.5 bg-primary-500 rounded-full" />
                  <span>{highlight}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-6">
            {pkg.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-neutral-100 text-neutral-700 px-2 py-1 rounded text-xs"
              >
                {tag}
              </span>
            ))}
          </div>

          {/* Price and CTA */}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600">Starting from</p>
              <p className="text-2xl font-bold text-primary-600">{pkg.priceRange.split(' - ')[0]}</p>
              <p className="text-xs text-neutral-500">per person</p>
            </div>
            <button
              type="button"
              onClick={onCustomize}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300"
            >
              Customize Trip
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
